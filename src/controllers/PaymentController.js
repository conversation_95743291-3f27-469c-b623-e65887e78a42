import { PaymentService } from '../services/PaymentService.js';
import { WebhookService } from '../services/WebhookService.js';
import { ResponseUtil } from '../utils/response.js';
import { HTTP_STATUS, ERROR_MESSAGES } from '../utils/constants.js';
import logger from '../config/logger.js';

/**
 * Async handler wrapper for error handling
 * @param {Function} fn - Async function to wrap
 * @returns {Function} Wrapped function
 */
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

/**
 * PaymentController
 * Handles payment-related HTTP requests
 */
export class PaymentController {
  /**
   * Verify payment and process transaction
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static verifyPayment = asyncHandler(async (req, res) => {
    try {
      const { razorpay_payment_id, razorpay_order_id, razorpay_signature } = req.body;

      if (!razorpay_payment_id || !razorpay_order_id || !razorpay_signature) {
        ResponseUtil.badRequest(res, 'Missing required payment verification parameters');
        return;
      }

      const result = await PaymentService.verifyAndProcessPayment({
        razorpay_payment_id,
        razorpay_order_id,
        razorpay_signature,
      });

      ResponseUtil.success(res, 'Payment verified and processed successfully', {
        transactionId: result.transaction.id,
        status: result.transaction.status,
        type: result.result.type,
        details: result.result,
      });
    } catch (error) {
      logger.error('Error verifying payment:', error);
      
      if (error.message.includes('Invalid payment signature')) {
        ResponseUtil.badRequest(res, 'Invalid payment signature');
      } else if (error.message.includes('not found')) {
        ResponseUtil.notFound(res, error.message);
      } else {
        ResponseUtil.error(res, ERROR_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR);
      }
    }
  });

  /**
   * Handle Razorpay webhooks
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static handleWebhook = asyncHandler(async (req, res) => {
    try {
      const signature = req.headers['x-razorpay-signature'];
      
      if (!signature) {
        ResponseUtil.badRequest(res, 'Missing webhook signature');
        return;
      }

      const result = await WebhookService.processWebhook(req.body, signature);
      
      ResponseUtil.success(res, result.message || 'Webhook processed successfully', {
        event: req.body.event,
        processed: result.success,
      });
    } catch (error) {
      logger.error('Error processing webhook:', error);
      
      if (error.message.includes('Invalid webhook signature')) {
        ResponseUtil.badRequest(res, 'Invalid webhook signature');
      } else {
        ResponseUtil.error(res, ERROR_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR);
      }
    }
  });

  /**
   * Get user's payment history
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getPaymentHistory = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    try {
      const limit = parseInt(req.query.limit) || 10;
      const offset = parseInt(req.query.offset) || 0;

      const result = await PaymentService.getUserPaymentHistory(req.user.userId, limit, offset);
      
      ResponseUtil.success(res, 'Payment history retrieved successfully', {
        transactions: result.transactions.map(transaction => ({
          id: transaction.id,
          type: transaction.transactionType,
          amount: parseFloat(transaction.amount),
          currency: transaction.currency,
          status: transaction.status,
          paymentMethod: transaction.paymentMethod,
          paidAt: transaction.paidAt,
          createdAt: transaction.createdAt,
          failureReason: transaction.failureReason,
        })),
        pagination: result.pagination,
      });
    } catch (error) {
      logger.error('Error fetching payment history:', error);
      ResponseUtil.error(res, ERROR_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR);
    }
  });

  /**
   * Get payment statistics
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getPaymentStats = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    try {
      const stats = await PaymentService.getPaymentStats(req.user.userId);
      
      ResponseUtil.success(res, 'Payment statistics retrieved successfully', {
        totalTransactions: stats.totalTransactions,
        totalAmount: stats.totalAmount,
        successfulTransactions: stats.successfulTransactions,
        successfulAmount: stats.successfulAmount,
        successRate: stats.totalTransactions > 0 
          ? ((stats.successfulTransactions / stats.totalTransactions) * 100).toFixed(2)
          : 0,
        recentTransactions: stats.recentTransactions.map(transaction => ({
          id: transaction.id,
          type: transaction.transactionType,
          amount: parseFloat(transaction.amount),
          currency: transaction.currency,
          status: transaction.status,
          paidAt: transaction.paidAt,
        })),
      });
    } catch (error) {
      logger.error('Error fetching payment stats:', error);
      ResponseUtil.error(res, ERROR_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR);
    }
  });

  /**
   * Get transaction details
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getTransactionDetails = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    try {
      const { transactionId } = req.params;
      
      const { PaymentTransaction } = await import('../models/subscription/index.js');
      const transaction = await PaymentTransaction.findOne({
        where: {
          id: transactionId,
          userId: req.user.userId,
        },
        include: [
          {
            model: (await import('../models/subscription/index.js')).SubscriptionPlan,
            as: 'plan',
          },
        ],
      });

      if (!transaction) {
        ResponseUtil.notFound(res, 'Transaction not found');
        return;
      }

      ResponseUtil.success(res, 'Transaction details retrieved successfully', {
        transaction: {
          id: transaction.id,
          type: transaction.transactionType,
          amount: parseFloat(transaction.amount),
          currency: transaction.currency,
          status: transaction.status,
          razorpayOrderId: transaction.razorpayOrderId,
          razorpayPaymentId: transaction.razorpayPaymentId,
          paymentMethod: transaction.paymentMethod,
          paidAt: transaction.paidAt,
          failureReason: transaction.failureReason,
          createdAt: transaction.createdAt,
          plan: transaction.plan ? {
            id: transaction.plan.id,
            type: transaction.plan.planType,
            name: transaction.plan.name,
          } : null,
        },
      });
    } catch (error) {
      logger.error('Error fetching transaction details:', error);
      ResponseUtil.error(res, ERROR_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR);
    }
  });

  /**
   * Manually sync payment status (development only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static syncPaymentStatus = asyncHandler(async (req, res) => {
    if (process.env.NODE_ENV === 'production') {
      ResponseUtil.forbidden(res, 'Manual sync not allowed in production');
      return;
    }

    try {
      const { razorpayPaymentId } = req.params;

      const { RazorpayService } = await import('../services/RazorpayService.js');
      const razorpayService = RazorpayService.getInstance();

      // Fetch payment details from Razorpay
      const payment = await razorpayService.fetchPayment(razorpayPaymentId);

      // Process based on payment status
      if (payment.status === 'captured') {
        // Simulate webhook event
        await WebhookService.handlePaymentCaptured(payment);
      } else if (payment.status === 'failed') {
        await WebhookService.handlePaymentFailed(payment);
      }

      ResponseUtil.success(res, 'Payment status synced successfully', {
        paymentId: payment.id,
        status: payment.status,
        amount: payment.amount / 100,
      });
    } catch (error) {
      logger.error('Error syncing payment status:', error);
      ResponseUtil.error(res, ERROR_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR);
    }
  });

  /**
   * Request payment refund (admin only)
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static requestRefund = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    try {
      const { transactionId } = req.params;
      const { amount, reason } = req.body;

      // Note: In a real application, you'd want to add admin authorization here
      // For now, we'll allow users to request refunds for their own transactions

      const { PaymentTransaction } = await import('../models/subscription/index.js');
      const transaction = await PaymentTransaction.findOne({
        where: {
          id: transactionId,
          userId: req.user.userId,
        },
      });

      if (!transaction) {
        ResponseUtil.notFound(res, 'Transaction not found');
        return;
      }

      if (!transaction.razorpayPaymentId) {
        ResponseUtil.badRequest(res, 'Cannot refund transaction without payment ID');
        return;
      }

      const result = await PaymentService.refundPayment(
        transaction.razorpayPaymentId,
        amount,
        reason || 'Refund requested by user'
      );

      ResponseUtil.success(res, 'Refund processed successfully', {
        refundId: result.refund.id,
        amount: result.refund.amount / 100, // Convert from paise
        status: result.refund.status,
        transaction: {
          id: result.transaction.id,
          status: result.transaction.status,
        },
      });
    } catch (error) {
      logger.error('Error processing refund:', error);
      
      if (error.message.includes('Cannot refund')) {
        ResponseUtil.badRequest(res, error.message);
      } else {
        ResponseUtil.error(res, ERROR_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR);
      }
    }
  });
}
